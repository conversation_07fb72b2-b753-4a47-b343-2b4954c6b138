{"COMPONENT_KCONFIGS": "/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/log/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/usb/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader/Kconfig.projbuild;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format/Kconfig.projbuild;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/Kconfig.projbuild;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/Kconfig.projbuild;/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32p4", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.4.1", "IDF_ENV_FPGA": "", "IDF_PATH": "/Users/<USER>/esp/v5.4.1/esp-idf", "COMPONENT_KCONFIGS_SOURCE_FILE": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/kconfigs_projbuild.in"}