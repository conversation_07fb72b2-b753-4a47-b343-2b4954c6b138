# ninja log v6
14	21	1752514690719311360	project_elf_src_esp32p4.c	c8d5842d3203c703
14	21	1752514690719311360	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/project_elf_src_esp32p4.c	c8d5842d3203c703
13	36	1752514690734263474	esp-idf/esp_system/ld/memory.ld	9b531a7fca2544ed
13	36	1752514690734263474	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_system/ld/memory.ld	9b531a7fca2544ed
13	36	1752514690734683140	esp-idf/esp_system/ld/sections.ld.in	7c83540299ad2a49
13	36	1752514690734683140	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_system/ld/sections.ld.in	7c83540299ad2a49
13	154	1752514690744521133	partition_table/partition-table.bin	140ea406e299a6a
13	154	1752514690744521133	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/partition_table/partition-table.bin	140ea406e299a6a
38	156	1752514690737402680	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/abort.c.obj	b34b810c4c9f857c
39	157	1752514690738078971	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/heap.c.obj	70ec1b6cfc093b0f
38	160	1752514690737563971	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/assert.c.obj	fbec3a6eb58298ba
36	162	1752514690735511640	esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_exception_stubs.cpp.obj	a175ad4ff5f8da12
37	164	1752514690735929723	esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_init.cpp.obj	7ad12ef1622bc3a
38	184	1752514690737107430	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_semaphore.c.obj	fd1a2088b2774881
37	197	1752514690736683181	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_local_storage.c.obj	92a60b4d74ec8edf
38	214	1752514690736933514	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_rwlock.c.obj	5c9e372b530d3505
154	215	1752514690853379635	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/flockfile.c.obj	f9cff048b943d798
37	234	1752514690736077639	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread.c.obj	dbbf32388a39b47c
37	235	1752514690736215764	esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_cond_var.c.obj	ec84aab85b0f587f
160	235	1752514690858930339	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/pthread.c.obj	43a995d9c78cd8a
165	242	1752514690863843961	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/getentropy.c.obj	fab26d8c97eb6576
157	242	1752514690856329925	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/poll.c.obj	136b574399180410
162	243	1752514690861596171	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/random.c.obj	e006db12b4c6f93c
215	248	1752514690914610298	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/termios.c.obj	c6c3dea472409fa3
184	256	1752514690883070571	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/reent_init.c.obj	3243d289539a2dd1
197	289	1752514690896570895	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/newlib_init.c.obj	2ded896910a85218
156	294	1752514690855549467	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/locks.c.obj	3cbb921bd621ccbb
235	303	1752514690934744033	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/sysconf.c.obj	eba0ac90ef7b896a
242	316	1752514690941108070	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/realpath.c.obj	5c7857b1f2d9e302
243	320	1752514690941801194	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/scandir.c.obj	d793c3d240fed3e
243	326	1752514690942107569	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/port/esp_time_impl.c.obj	68ac6b70b55bc80b
214	328	1752514690913727174	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/syscalls.c.obj	6995cd2330eb5d57
256	342	1752514690955356476	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/app_startup.c.obj	a2b22bf960a3b9a9
248	348	1752514690947448148	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/heap_idf.c.obj	1beb5bee2b2d76e0
289	368	1752514690988604576	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_common.c.obj	f622200bcce7c6ec
235	375	1752514690934398866	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/time.c.obj	580280d7e9007c34
303	386	1752514691001960066	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/list.c.obj	d79a29407e24dc46
294	394	1752514690993643739	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_systick.c.obj	51d504df735bd9c9
234	401	1752514690933409284	esp-idf/newlib/CMakeFiles/__idf_newlib.dir/stdatomic.c.obj	f43e63159a1706a2
368	407	1752514691067684850	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/riscv/portasm.S.obj	5f7225b7c5200170
329	445	1752514691028040463	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/event_groups.c.obj	50da1431e2e5ccb2
386	454	1752514691085328045	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions_event_groups.c.obj	442e7a8a89cb0fa
375	457	1752514691073981554	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/freertos_compatibility.c.obj	801ef4e51a1b625b
326	464	1752514691025246882	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/timers.c.obj	10e24badf3952214
407	473	1752514691105986197	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/esp_cpu_intr.c.obj	4d15e88b1fedbf70
394	484	1752514691093325331	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions.c.obj	c2d4700052efe715
401	495	1752514691100767409	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	7844712bc3501689
342	495	1752514691041698953	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/stream_buffer.c.obj	f2685c4a8d7c0429
445	496	1752514691144361335	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	bbdcb005721e76ac
473	543	1752514691172013189	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/hw_random.c.obj	1c35c2aa706b6eb2
454	550	1752514691153616536	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/cpu_region_protect.c.obj	fce2a9fe270d3105
496	553	1752514691194814964	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/revision.c.obj	25405a3fb61f944
317	560	1752514691015874097	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/queue.c.obj	7efeed8a0af49d7
37	560	1752514690735778348	esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_guards.cpp.obj	3650ded31876ace2
348	563	1752514691046861741	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/riscv/port.c.obj	39a434151e09bf7a
495	589	1752514691194558756	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/periph_ctrl.c.obj	2abbd4a445d5271a
495	596	1752514691194210381	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mac_addr.c.obj	f43cf6094b598b58
464	596	1752514691163375362	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clk_ctrl_os.c.obj	fbf3fe7ba5b3bd8a
457	602	1752514691156586451	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clk.c.obj	b67768406aae5c81
550	627	1752514691249363340	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modem.c.obj	c20336217b785133
560	645	1752514691258962499	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_console.c.obj	46bb60a328a3f71c
602	656	1752514691301622301	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_gpio_reserve.c.obj	4d537c04a3f543ab
564	657	1752514691263426830	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_usb.c.obj	d632bab07c794af0
543	662	1752514691242532262	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rtc_module.c.obj	b81f794ef7a2fad8
561	667	1752514691259919707	esp-idf/cxx/libcxx.a	58bfed3e7a491091
596	675	1752514691294971098	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_event.c.obj	aeb69d029332d3ba
484	678	1752514691183595181	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/intr_alloc.c.obj	c481399753a08e59
596	718	1752514691295453806	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/regi2c_ctrl.c.obj	311c31a863a0f7ec
667	735	1752514691366093086	esp-idf/pthread/libpthread.a	e4c32647e2f43e77
627	765	1752514691326271366	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sar_periph_ctrl_common.c.obj	53fd6cca04543908
658	770	1752514691356834510	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp_clk_tree_common.c.obj	b7e2dc339e995c9a
656	789	1752514691355438053	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/esp_clk_tree.c.obj	62c2eae22b86f00f
646	799	1752514691344836019	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/io_mux.c.obj	25ad3b8b3077a7
675	802	1752514691374734913	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma_link.c.obj	1abf410e72c426c0
735	803	1752514691434322952	esp-idf/newlib/libnewlib.a	386e93b2d3f5484a
678	822	1752514691377597703	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/spi_share_hw_ctrl.c.obj	e87fb7519b98075a
662	828	1752514691361232673	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/esp_dma_utils.c.obj	ce403fafb738bc6
765	843	1752514691464148930	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clk_utils.c.obj	836f0cd457a55536
589	858	1752514691288188061	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_gpio.c.obj	3df0fd0d12d908d5
718	868	1752514691417138882	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/spi_bus_lock.c.obj	4069c7131ace9c80
802	881	1752514691501728777	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_system_peripheral.c.obj	f55e10784fe1b3ce
789	891	1752514691488524287	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mipi_csi_share_hw_ctrl.c.obj	2004941a1ffb615b
770	894	1752514691469582468	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/adc_share_hw_ctrl.c.obj	d9a0068567ff5107
828	897	1752514691527497341	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma_sleep_retention.c.obj	5c539d2475e1a1d3
823	932	1752514691521999928	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/deprecated/gdma_legacy.c.obj	509a9f60380a6d23
859	955	1752514691557921860	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma_crc.c.obj	47ab3d6f801f552
891	963	1752514691590754627	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/esp_async_memcpy.c.obj	6c0717f820e0e912
321	976	1752514691020004427	esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/tasks.c.obj	288598b66a9e631b
843	980	1752514691542324580	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma_etm.c.obj	ed897d8bc3ea552c
955	999	1752514691654640913	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/systimer.c.obj	4259b58990ad4cb7
881	1013	1752514691580178343	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/debug_probe/debug_probe.c.obj	39d316d579646c2
868	1047	1752514691567427020	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/ldo/esp_ldo_regulator.c.obj	fc2c564208ffa2e
799	1049	1752514691498472529	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_retention.c.obj	3d3fb49f1885b1b2
978	1050	1752514691676932605	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/pau_regdma.c.obj	504dfd887ec91bdc
963	1094	1752514691662059199	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_etm.c.obj	8896fa7dceb9c1c3
803	1110	1752514691502038318	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma.c.obj	6c959db376aea3bd
1000	1115	1752514691698931588	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mspi_timing_tuning.c.obj	d623b1ca1aea0f29
894	1124	1752514691593594084	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/async_memcpy_gdma.c.obj	b80570bebe322710
1013	1140	1752514691712538120	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mspi_timing_by_dqs.c.obj	add3fcf6eb5c36da
1049	1155	1752514691748386051	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/rtc_clk_init.c.obj	7e9fafcea63846a6
1094	1158	1752514691793265726	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/pmu_param.c.obj	6a6af48695d24cf5
1140	1199	1752514691839579108	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/chip_info.c.obj	42f92256a1380c33
1111	1202	1752514691810361297	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/pmu_init.c.obj	965d64efa3fb96e
553	1208	1752514691251879755	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modes.c.obj	461929c6845ded11
897	1230	1752514691596045373	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/dw_gdma.c.obj	5425cce04e7e1d37
1158	1236	1752514691857223720	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/mspi_timing_config.c.obj	ee58c0d53019d2fb
1124	1243	1752514691823026996	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/rtc_time.c.obj	7b1cd5f8ca31b5ec
1048	1245	1752514691746797302	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clock_output.c.obj	69859fcb78fcb0ce
1155	1249	1752514691854405264	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/sar_periph_ctrl.c.obj	8577c477a3c9d612
1202	1271	1752514691900969771	esp-idf/freertos/libfreertos.a	fe8490d0f8064a37
1199	1280	1752514691897900690	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/lowpower/port/esp32p4/sleep_clock.c.obj	2b76c4541d3f4d9b
1243	1290	1752514691942740240	esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/esp_crypto_lock.c.obj	503a21372daadbba
1245	1292	1752514691944477488	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	5c38c1737f331b93
1208	1298	1752514691907537266	esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/init.c.obj	ad75b19ba63d4da9
932	1307	1752514691631284764	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/dma2d.c.obj	690135306250e995
1249	1310	1752514691948417944	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	22f824633e45bf70
1271	1310	1752514691970384136	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/interrupts.c.obj	616bcc43558cc6df
1280	1333	1752514691979625296	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/gpio_periph.c.obj	b089178fc49e5bf1
1050	1340	1752514691749758634	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/rtc_clk.c.obj	e01be0533d4ba2d7
1292	1340	1752514691991422204	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/adc_periph.c.obj	be0fdda33742663a
1230	1341	1752514691929713166	esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/esp_hmac.c.obj	a3f53cca10e1a008
1290	1341	1752514691989496455	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/uart_periph.c.obj	5f6b78296585c15c
1310	1344	1752514692009143315	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/debug_probe_periph.c.obj	5ee01da93b0f1423
1298	1345	1752514691997738032	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/ana_cmpr_periph.c.obj	9886c1793a9654bf
980	1346	1752514691679462311	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/regdma_link.c.obj	5b687dfb0b9c89e
1310	1350	1752514692009499857	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/emac_periph.c.obj	4f9940ed928ad6a0
1307	1351	1752514692006442401	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/dedic_gpio_periph.c.obj	c66deeb4be5ad330
1236	1358	1752514691935409995	esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/esp_ds.c.obj	bad14f1bf7bac40d
1340	1377	1752514692039544876	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/dma2d_periph.c.obj	f538b8c1e98fc661
1334	1383	1752514692033622714	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/etm_periph.c.obj	13ea618ff11c0ec1
1346	1383	1752514692045502413	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/sdm_periph.c.obj	891e98a1a7e6670e
1345	1390	1752514692044486956	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/rmt_periph.c.obj	1eadcc246dc5f196
1344	1393	1752514692042879165	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/pcnt_periph.c.obj	fab8491174e603da
1340	1394	1752514692039190210	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/gdma_periph.c.obj	4a08face771f6b81
1341	1396	1752514692040711000	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/ledc_periph.c.obj	ee6bfab454fc3d01
1350	1397	1752514692049371910	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/isp_periph.c.obj	890e13c25e4353
1341	1399	1752514692040317917	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/spi_periph.c.obj	bd209b918bb2a185
1351	1412	1752514692050737451	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/i2s_periph.c.obj	e4cf52e7c6a472a9
1115	1418	1752514691814732752	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/pmu_sleep.c.obj	4ffd9e3fd5f5f344
1358	1420	1752514692057377446	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/i2c_periph.c.obj	fc3f2d86ea7569d5
1384	1423	1752514692082785886	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/lcd_periph.c.obj	e243c427e10c8d2a
1378	1429	1752514692076815140	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/temperature_sensor_periph.c.obj	1d46e7d4798b9909
1390	1430	1752514692089444464	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/mipi_dsi_periph.c.obj	f57829bc11fd7d45
1383	1432	1752514692082427177	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/timer_periph.c.obj	affa799648e25d69
1412	1443	1752514692111117948	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/touch_sensor_periph.c.obj	20ab92010d93af7d
1393	1444	1752514692092378462	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/mipi_csi_periph.c.obj	10b3559e22b68b22
1394	1445	1752514692093196253	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/parlio_periph.c.obj	694aa31cb02424c4
1396	1450	1752514692094934793	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/mcpwm_periph.c.obj	a1995172f2dbc63d
1397	1456	1752514692096404834	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/mpi_periph.c.obj	dd61b50bfd439dc3
1399	1457	1752514692098516249	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/sdmmc_periph.c.obj	92afb1600d35aaa1
1423	1469	1752514692122710106	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/wdt_periph.c.obj	a6b296054c0ab31a
1429	1471	1752514692127968477	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/usb_dwc_periph.c.obj	5cb737a7bf50a294
1430	1482	1752514692129595851	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/rtc_io_periph.c.obj	4c9235b61f86c1e9
1420	1484	1752514692119779650	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/twai_periph.c.obj	8989f501eb465211
1443	1485	1752514692142697424	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/cam_periph.c.obj	4925ae38ee29e591
1432	1511	1752514692131090849	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/system_retention_periph.c.obj	8d61f79d9383cfc5
1471	1527	1752514692170742278	esp-idf/heap/CMakeFiles/__idf_heap.dir/port/esp32p4/memory_layout.c.obj	d76c0a9cd65c256f
1444	1554	1752514692143108382	esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_base.c.obj	39c314640ebc0879
1450	1555	1752514692149748794	esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_init.c.obj	6be829fa0bb279c3
1469	1557	1752514692168407822	esp-idf/heap/CMakeFiles/__idf_heap.dir/port/memory_layout_utils.c.obj	9b1377e942d22aac
1482	1560	1752514692181241562	esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_timestamp.c.obj	d2297aefecf99df0
1485	1566	1752514692183816477	esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_lock.c.obj	17d5068ea0d66096
1511	1582	1752514692210343665	esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_write.c.obj	c1cd62b611b88c40
1418	1592	1752514692117088027	esp-idf/esp_hw_support/libesp_hw_support.a	38e55c5e7ee67b6
1528	1593	1752514692226891028	esp-idf/log/CMakeFiles/__idf_log.dir/src/buffer/log_buffers.c.obj	d637afa556843052
1556	1594	1752514692254852965	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/log_level.c.obj	85ee1c3dbea98a7a
1554	1594	1752514692253268217	esp-idf/log/CMakeFiles/__idf_log.dir/src/util.c.obj	39c97da18eee5e1c
1456	1595	1752514692155567956	esp-idf/heap/CMakeFiles/__idf_heap.dir/multi_heap.c.obj	78ba84bb552e7213
1484	1603	1752514692182885311	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	f2f45a24b85226dd
1557	1616	1752514692256544256	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/tag_log_level.c.obj	7d6cd6a96906a98d
1560	1630	1752514692259198254	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/linked_list/log_linked_list.c.obj	ac3efe1d24668d
1566	1635	1752514692265378124	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/cache/log_binary_heap.c.obj	298a568e1f235466
1593	1635	1752514692291852521	esp-idf/esp_security/libesp_security.a	c37d1cf60c230ec5
1445	1637	1752514692144420423	esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps.c.obj	d3a263ae7216787a
1582	1654	1752514692281721862	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	1c143d6ece9418ee
1594	1659	1752514692293751687	esp-idf/hal/CMakeFiles/__idf_hal.dir/lp_timer_hal.c.obj	d6dc16c7b4f4137c
1616	1663	1752514692315656670	esp-idf/hal/CMakeFiles/__idf_hal.dir/color_hal.c.obj	f562e8427ed5fbba
1593	1670	1752514692292702271	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	1ff9ef6533539efd
1594	1676	1752514692293087104	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32p4/efuse_hal.c.obj	533e5482913f8d6e
1637	1699	1752514692336549696	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_encrypt_hal_iram.c.obj	6e4bd3e0f3a06971
1635	1729	1752514692334565698	esp-idf/soc/libsoc.a	42227cbba22bcc1
1663	1732	1752514692362415135	esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal_iram.c.obj	79b0deb216987842
1595	1766	1752514692294730061	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	bb4d760a81512874
1699	1766	1752514692398426692	esp-idf/hal/CMakeFiles/__idf_hal.dir/timer_hal.c.obj	cabbefcd5d3f711e
1671	1775	1752514692369820672	esp-idf/hal/CMakeFiles/__idf_hal.dir/gpio_hal.c.obj	cbc7a92b125691ad
1603	1779	1752514692301895347	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	935965042a2c16aa
1677	1782	1752514692375867125	esp-idf/hal/CMakeFiles/__idf_hal.dir/rtc_io_hal.c.obj	e1f122849baa7005
1654	1814	1752514692353276726	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32p4/clk_tree_hal.c.obj	13888dc9c188780a
1729	1815	1752514692428341669	esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal.c.obj	4a695b4b59e46d1
1732	1816	1752514692431555500	esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal_iram.c.obj	7959beb9c25f0ef
1659	1831	1752514692357848139	esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal.c.obj	b270c0ab3a360a2c
1766	1846	1752514692465712100	esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal_iram.c.obj	1e3e61c5a44fc715
1766	1851	1752514692465095434	esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal.c.obj	a2d1a2f0c049cd30
1782	1853	1752514692481568963	esp-idf/hal/CMakeFiles/__idf_hal.dir/rmt_hal.c.obj	e08c6c0d95b0c9fc
1779	1853	1752514692478356424	esp-idf/hal/CMakeFiles/__idf_hal.dir/lp_i2s_hal.c.obj	b3bd26547ed22608
1630	1867	1752514692329305452	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal.c.obj	23cf8e4f2b116fc1
1635	1873	1752514692334047823	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_iram.c.obj	982cf3745e4528b5
1814	1875	1752514692513751273	esp-idf/hal/CMakeFiles/__idf_hal.dir/pcnt_hal.c.obj	e11e99d5c3c54b22
1851	1910	1752514692550733370	esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal_crc_gen.c.obj	54439008f651f7f6
1831	1911	1752514692530149760	esp-idf/hal/CMakeFiles/__idf_hal.dir/twai_hal_iram.c.obj	70f3d797e9b3e2cf
1816	1924	1752514692515716063	esp-idf/hal/CMakeFiles/__idf_hal.dir/twai_hal.c.obj	4a05aa5949d46ed5
1815	1926	1752514692514543230	esp-idf/hal/CMakeFiles/__idf_hal.dir/mcpwm_hal.c.obj	fd3b5698328b7319
1873	1942	1752514692572649854	esp-idf/hal/CMakeFiles/__idf_hal.dir/dma2d_hal.c.obj	c009db4b0e5524a0
1867	1944	1752514692566083025	esp-idf/hal/CMakeFiles/__idf_hal.dir/dw_gdma_hal.c.obj	9a4662ed2ca6c4dd
1846	1947	1752514692545442499	esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal_top.c.obj	e43bc84128810b10
1910	1966	1752514692609350785	esp-idf/hal/CMakeFiles/__idf_hal.dir/sdm_hal.c.obj	46763ea50a462134
1911	1976	1752514692610819950	esp-idf/hal/CMakeFiles/__idf_hal.dir/sdmmc_hal.c.obj	be1e15a45656b3b4
1775	1983	1752514692473894094	esp-idf/hal/CMakeFiles/__idf_hal.dir/isp_hal.c.obj	38b5056d7f42952b
1853	1988	1752514692552395161	esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal_axi.c.obj	178ca7ecf883de17
1926	1988	1752514692625171231	esp-idf/hal/CMakeFiles/__idf_hal.dir/parlio_hal.c.obj	afe08d86e5167503
1924	1995	1752514692623434524	esp-idf/hal/CMakeFiles/__idf_hal.dir/etm_hal.c.obj	173d70d4a288226a
1853	2005	1752514692552001702	esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal_ahb_v2.c.obj	3d5e7ad4ad025c08
1942	2018	1752514692641455927	esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal_common.c.obj	fdf64dba8d586ec7
1966	2029	1752514692665021993	esp-idf/hal/CMakeFiles/__idf_hal.dir/lcd_hal.c.obj	dfd96313ff7f639e
1944	2032	1752514692643353343	esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_oneshot_hal.c.obj	fd9142209191e815
1983	2077	1752514692682681355	esp-idf/hal/CMakeFiles/__idf_hal.dir/mipi_csi_hal.c.obj	e8f5953b16714e39
1989	2092	1752514692687788143	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpi_hal.c.obj	1205fb7cea28bb5
1988	2100	1752514692687172727	esp-idf/hal/CMakeFiles/__idf_hal.dir/ecc_hal.c.obj	eba7860001495e9e
2018	2128	1752514692717657704	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32p4/pau_hal.c.obj	e639ac9505a03071
1995	2130	1752514692694234763	esp-idf/hal/CMakeFiles/__idf_hal.dir/sha_hal.c.obj	38ac18fa75f1522f
1947	2141	1752514692646494007	esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal.c.obj	68d53dfcc6b8eae6
2005	2142	1752514692704348589	esp-idf/hal/CMakeFiles/__idf_hal.dir/aes_hal.c.obj	453efbd8e381c958
2029	2146	1752514692727999863	esp-idf/hal/CMakeFiles/__idf_hal.dir/brownout_hal.c.obj	e7ad47734b625c6c
1875	2151	1752514692574450352	esp-idf/hal/CMakeFiles/__idf_hal.dir/i2s_hal.c.obj	46ccfb512fbaa97
1457	2152	1752514692155929914	esp-idf/heap/CMakeFiles/__idf_heap.dir/tlsf/tlsf.c.obj	b45ba257979d6c92
1976	2155	1752514692675548194	esp-idf/hal/CMakeFiles/__idf_hal.dir/mipi_dsi_hal.c.obj	d3a6fef73b1af64e
2077	2183	1752514692776379368	esp-idf/hal/CMakeFiles/__idf_hal.dir/ppa_hal.c.obj	4bfeecb3c77cf1de
2032	2194	1752514692731690444	esp-idf/hal/CMakeFiles/__idf_hal.dir/jpeg_hal.c.obj	a74a10fa40097eed
2152	2195	1752514692850918021	esp-idf/heap/libheap.a	794fc93eee65dc12
2128	2221	1752514692827745289	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal.c.obj	7ec037f050a1564
2130	2222	1752514692829178662	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal_iram.c.obj	b14ecbe3c4888a4a
2146	2223	1752514692845130651	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32p4/pmu_hal.c.obj	43a42b1928ef7816
2155	2226	1752514692854622643	esp-idf/hal/CMakeFiles/__idf_hal.dir/hmac_hal.c.obj	66f2df4a1daa15a7
2093	2258	1752514692791808065	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal.c.obj	6da2b968498885e6
2151	2259	1752514692850437272	esp-idf/hal/CMakeFiles/__idf_hal.dir/apm_hal.c.obj	d6fc495cee784bf3
2183	2263	1752514692882259790	esp-idf/hal/CMakeFiles/__idf_hal.dir/ds_hal.c.obj	349e2126f05f130a
2195	2272	1752514692894725905	esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_serial_jtag_hal.c.obj	d8c7ce81bbea8fd5
2194	2279	1752514692893599448	esp-idf/hal/CMakeFiles/__idf_hal.dir/cam_hal.c.obj	de59cf4269b13d52
2222	2283	1752514692920890386	esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_utmi_hal.c.obj	c35ec71097b0bb5c
2223	2286	1752514692921826968	esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_wrap_hal.c.obj	413664c925afd13e
2100	2300	1752514692799473601	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal_iram.c.obj	79e3f4f7617d3c6d
2272	2307	1752514692971336848	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	a7ac516bd74772e1
2258	2312	1752514692957182609	esp-idf/log/liblog.a	9974124808eeb633
2286	2328	1752514692985230421	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	56b1bf49a98d0e1e
2259	2330	1752514692958253358	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	3404531071b5790d
2264	2337	1752514692962943146	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	ec193b891388dd8c
2141	2343	1752514692840571196	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hd_hal.c.obj	c558079ee2472dd6
2300	2345	1752514692999199077	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	e3cea2f8ceddb7be
2142	2352	1752514692841140404	esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_gpspi.c.obj	33ad05e58766b091
2283	2354	1752514692982000674	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	adf086726a0194fd
2312	2365	1752514693011117527	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	d6fd3665c157e0eb
2226	2370	1752514692924951716	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32p4/touch_sensor_hal.c.obj	250a06d2ba0321a9
2279	2370	1752514692977895718	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	c6de435fadadf376
2307	2372	1752514693006331822	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_regi2c_esp32p4.c.obj	7ef20626a3f711f3
2330	2392	1752514693029182472	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_clic.c.obj	a0f8fecc770b06b
2328	2418	1752514693027233848	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	a28614d517c87a23
2343	2421	1752514693042573378	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	8fc10d1f39de9418
2337	2433	1752514693036615425	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	9978f0e7f0200d0c
2371	2444	1752514693069794066	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_system.c.obj	9a7179b94080ebe0
2345	2450	1752514693044617710	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/crosscore_int.c.obj	5242d9162cf550dc
2372	2451	1752514693071748023	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup.c.obj	2bb99750ce0624e0
2222	2453	1752514692921344052	esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_dwc_hal.c.obj	2b423f3aa4860b02
2421	2477	1752514693120031654	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/stack_check.c.obj	b88b0392dcfc6ebb
2354	2479	1752514693052854496	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/freertos_hooks.c.obj	d7347eb6cd5170f9
2352	2483	1752514693051462330	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_ipc.c.obj	a5a1c98e017bfa01
2365	2498	1752514693063888946	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/int_wdt.c.obj	d441a900c20a67dd
2418	2500	1752514693116966281	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/system_time.c.obj	1ea729a5479b1a7a
2444	2508	1752514693143607803	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/xt_wdt.c.obj	9052407a0e12b732
2433	2523	1752514693132616645	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/ubsan.c.obj	f1513452c90f8db5
2477	2552	1752514693176314821	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/systick_etm.c.obj	11b7277409dec070
2370	2557	1752514693068955150	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/panic.c.obj	e3e8dc83d6120f84
2479	2573	1752514693178438652	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/hw_stack_guard.c.obj	f8cd74d92e502f10
2498	2599	1752514693197461138	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/panic_handler.c.obj	310c75983e4d8357
2451	2601	1752514693150602381	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt_impl_timergroup.c.obj	1fc118ac6aa72b45
2454	2604	1752514693153272171	esp-idf/hal/libhal.a	8711409a226439fe
2573	2606	1752514693272224749	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/esp_ipc_isr_handler.S.obj	6cc4aa0dfbea9037
2508	2614	1752514693207013714	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/image_process.c.obj	d5aea7397655c1fe
2500	2622	1752514693199217970	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/esp_system_chip.c.obj	c91a892856aca00c
2557	2625	1752514693256257303	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/esp_ipc_isr_port.c.obj	ca8a5891fe38d4bf
2600	2647	1752514693298874271	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/esp_ipc_isr_routines.c.obj	69f2536dcfad9ebc
2554	2648	1752514693253344388	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/esp_ipc_isr.c.obj	dcbec218ff38471e
2605	2652	1752514693304276517	esp-idf/esp_rom/libesp_rom.a	4e16dc3fee71a502
2392	2656	1752514693091695217	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup_funcs.c.obj	66b0774aa15c6dcf
2450	2674	1752514693148852383	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt.c.obj	63806cd8194051fe
2523	2688	1752514693222594369	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/brownout.c.obj	66c3417e8793e695
2601	2694	1752514693300807686	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/expression_with_stack.c.obj	fd58fcaae60bc31
2656	2695	1752514693355469312	esp-idf/esp_common/libesp_common.a	548085ca84f67bcc
2614	2700	1752514693313772343	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/debug_helpers.c.obj	1d493cbd2c33a120
2622	2705	1752514693321603504	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/debug_stubs.c.obj	6ee548a07327ab25
2483	2711	1752514693182261483	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/cpu_start.c.obj	2c21046155b1c2fb
2607	2715	1752514693305781057	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/riscv/panic_arch.c.obj	af392668a06ea216
2674	2725	1752514693373778465	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_brownout_hook.c.obj	8f362a412c216cf8
2647	2736	1752514693346250694	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32p4/reset_reason.c.obj	7666b23539a38bf7
2688	2743	1752514693387542746	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_drivers.c.obj	3d485cebb14af59b
2695	2763	1752514693394358825	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_issi.c.obj	232f3a329480e8e0
2715	2784	1752514693414038102	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_boya.c.obj	4bacb7395de8706b
2712	2789	1752514693410971229	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_winbond.c.obj	21a75bdd26d532e6
2705	2790	1752514693404710233	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_gd.c.obj	ab86893cd235a767
2652	2797	1752514693350958315	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32p4/cache_err_int.c.obj	ebd0b5e2ff3fe811
2736	2817	1752514693435742544	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_th.c.obj	b0a9245535948d88
2700	2831	1752514693399460571	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic.c.obj	23d4d235de36e412
2648	2868	1752514693347495859	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32p4/system_internal.c.obj	4d40a8855fec5c9d
2790	2883	1752514693489826670	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	87baed32088855
2743	2898	1752514693442472997	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/memspi_host_driver.c.obj	88ba6f3ee2213d24
2725	2904	1752514693424685927	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic_opi.c.obj	1f47a20885d373e1
2786	2924	1752514693485507090	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_mmap.c.obj	124f687748fd3163
2789	2924	1752514693487883380	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_ops.c.obj	18b1707060cc689
2898	2935	1752514693597335423	esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/port/esp32p4/ext_mem_layout.c.obj	803e1c5760130f21
2764	2942	1752514693463293232	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/cache_utils.c.obj	96f120ecab494cbe
2868	2944	1752514693567379654	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_noos.c.obj	97feb1ae9ddc79c8
2831	2952	1752514693530600681	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_app.c.obj	ed460f16fa4c71ce
2625	2971	1752514693324676876	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32p4/clk.c.obj	e9f48f5027204a1e
2924	2982	1752514693623294321	esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/heap_align_hw.c.obj	939274fcfddb6aa8
2694	3000	1752514693393612492	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_generic.c.obj	a7f571e9a6213a75
2952	3011	1752514693651007008	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	685b593df5d136c3
2904	3014	1752514693603700127	esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_cache.c.obj	a937b556299d4148
2944	3017	1752514693643603347	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	f6574f1e37466c08
2942	3020	1752514693641349015	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	9832cdb242f19686
2982	3056	1752514693681342694	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	f42e8178b47c2783
2817	3067	1752514693516112275	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_spi_init.c.obj	7dba3ab263195678
2971	3069	1752514693670626244	esp-idf/esp_system/libesp_system.a	b2df1ec7717ef0b9
2925	3074	1752514693623800528	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	51f8d00b821d465c
2936	3080	1752514693634900604	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	8ba991c37e9496df
3014	3083	1752514693713257503	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32p4.c.obj	def196efc5bc8244
2883	3095	1752514693582271226	esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_mmu_map.c.obj	51c57bba1fe3407a
3001	3099	1752514693699816138	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	9d0d2e42e270f5d5
3012	3128	1752514693710899214	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	eaaed96cfe755222
3069	3140	1752514693768213504	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	f8fa71a69cf2df2c
3095	3150	1752514693793975652	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32p4/esp_efuse_table.c.obj	f7a44451b5d45200
3020	3152	1752514693719031708	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	11a6035efb7f48a9
2797	3155	1752514693496595373	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_api.c.obj	cb351d81d7ae3123
3083	3178	1752514693782205202	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32p4/secure_boot_secure_features.c.obj	edb4703de4bd9256
3099	3194	1752514693797845399	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32p4/esp_efuse_fields.c.obj	b15ad57f8cd116a3
3128	3207	1752514693827642293	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32p4/esp_efuse_rtc_calib.c.obj	ed18cbe893c0a6a0
3017	3221	1752514693716181293	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	c00c501a0a5129aa
3155	3243	1752514693854213815	esp-idf/spi_flash/libspi_flash.a	f99e723177eb624b
3080	3250	1752514693779318746	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/idf/bootloader_sha.c.obj	cfefe9add2fc1f39
3153	3254	1752514693852287275	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	287eb013a31d6f19
3056	3258	1752514693755534764	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32p4.c.obj	1907874cda181f7d
3243	3283	1752514693942374999	esp-idf/esp_mm/libesp_mm.a	7c33cb6204172efc
3140	3295	1752514693838926618	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32p4/esp_efuse_utility.c.obj	5ef3dd7f396b471f
3207	3300	1752514693906141985	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_startup.c.obj	2b3c25ce08adde75
3178	3302	1752514693877467881	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	7c3e642328429461
3150	3302	1752514693849270652	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	d6d0d105830eabdd
3258	3313	1752514693956965988	esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_app_desc.c.obj	eda324ae01ab76b5
3221	3327	1752514693920236974	esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition.c.obj	7cbcac699252da13
3250	3341	1752514693949472036	esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition_target.c.obj	876ef79d5d2e87d7
3194	3349	1752514693893746994	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	b0053b79e15e588f
3283	3350	1752514693982387011	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	cef28817610e9776
3300	3351	1752514694000654998	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/debug.c.obj	8d388705a53d4fd8
3302	3351	1752514694001211831	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_reader.c.obj	62bdd4cb67caa230
3302	3353	1752514694001743580	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_trace.c.obj	9e5e1fcc4c96ac47
3074	3359	1752514693773598584	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	3300c4fca39adbee
3350	3392	1752514694049451961	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_debug_helpers_generated.c.obj	39adf425eca74557
3297	3405	1752514693996688001	esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj	7d6cfca9f6a97a2b
3328	3451	1752514694026778478	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ciphersuites.c.obj	373a5e83b3d050d4
3405	3453	1752514694104529004	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_keys.c.obj	178de7a7303afcef
3349	3453	1752514694048686795	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cookie.c.obj	b052c0e6f079f662
3067	3454	1752514693766018589	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	f96344e2eb26fd81
3351	3476	1752514694050087627	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ticket.c.obj	de8d28f97dc2417c
3254	3478	1752514693953643491	esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_ops.c.obj	b2066f9448322b73
3314	3480	1752514694012801489	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cache.c.obj	bdbe762a1b739a9a
3451	3496	1752514694150491761	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_server.c.obj	7a5a0ed7d33e9ca5
3453	3499	1752514694152640426	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_generic.c.obj	f72a7f5def322655
3453	3500	1752514694152251801	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_client.c.obj	597567b9a08e016d
3454	3517	1752514694152986509	esp-idf/bootloader_support/libbootloader_support.a	c9c478f2e2f97aa7
3341	3533	1752514694040338260	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_client.c.obj	e901821af4f9bca6
3478	3542	1752514694177423533	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_platform_time.c.obj	ea00e27005e3e98d
3476	3555	1752514694175348368	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/mbedtls_debug.c.obj	2484bf1d698793d1
3517	3565	1752514694216246045	esp-idf/efuse/libefuse.a	99d0e9fa18f2b8db
3565	3608	1752514694264723884	esp-idf/esp_partition/libesp_partition.a	d3efa3b10e79656e
3499	3614	1752514694197968767	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_create.c.obj	a9f970edf670bd95
3608	3642	1752514694307413394	esp-idf/app_update/libapp_update.a	b189ca88ba1d66a2
3556	3653	1752514694254817600	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write.c.obj	b1301866cfebb899
3543	3677	1752514694241916651	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_csr.c.obj	37279d1804eadca
3480	3684	1752514694179655489	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/pkcs7.c.obj	4f1ccdacd742282b
3500	3684	1752514694199505349	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crl.c.obj	fab15fd530812e27
3642	3685	1752514694341603452	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	d71c4fd4b6d95191
3685	3720	1752514694384095212	esp-idf/esp_app_format/libesp_app_format.a	c165d77fbe9e94d5
3684	3733	1752514694383114296	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesni.c.obj	ce34ab2ea2584598
3685	3737	1752514694383814920	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesce.c.obj	2d98d0e7303d7ab1
3359	3757	1752514694058289996	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_client.c.obj	385acc3bf03693c3
3678	3767	1752514694376966675	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aes.c.obj	3fd0069f75318b8b
3653	3768	1752514694351969569	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_csr.c.obj	42f3456944fed9e6
3614	3773	1752514694313530348	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_crt.c.obj	b80b46891c6ca56c
3351	3823	1752514694049815794	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_msg.c.obj	94e0971107e5cc44
3392	3834	1752514694091582388	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_server.c.obj	6b0d4ed778e31b4b
3734	3834	1752514694433736342	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1parse.c.obj	66a03e402d259af0
3773	3844	1752514694472208188	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod.c.obj	dc9434df09ada700
3496	3847	1752514694195687977	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509.c.obj	6162f600ca51a566
3824	3871	1752514694522788234	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod_raw.c.obj	2c862f001f300c40
3757	3872	1752514694456450616	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/base64.c.obj	8b7850a6d02bc3fc
3834	3880	1752514694533319809	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/block_cipher.c.obj	e2b665b66758958a
3834	3882	1752514694533736684	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/camellia.c.obj	575d5a4b46e7914
3737	3888	1752514694435966590	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1write.c.obj	80b43e3d38afa262
3847	3895	1752514694546097341	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chacha20.c.obj	2eac44612a1c73d0
3720	3909	1752514694419456019	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aria.c.obj	78120679e04c6bb2
3871	3917	1752514694570689739	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chachapoly.c.obj	644fe9598b792a9c
3888	3942	1752514694587180144	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cmac.c.obj	c07a553873c9e234
3882	3952	1752514694581035357	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/constant_time.c.obj	5d4e34cc9952ce2d
3909	3955	1752514694607871503	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/des.c.obj	e0a1de93531bf97d
3917	3959	1752514694616337664	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/dhm.c.obj	e4bd0c2cfca6e849
3880	3967	1752514694579741483	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher_wrap.c.obj	4540344e4a133c57
3844	3983	1752514694543317510	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ccm.c.obj	9d8aa26a470bd1ad
3768	3986	1752514694467526650	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_core.c.obj	c9a0458bd8ff3b4d
3955	3995	1752514694653865052	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecjpake.c.obj	1ca6174659393c21
3895	4012	1752514694594737847	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ctr_drbg.c.obj	ccd8d767f4a20162
3942	4027	1752514694640908604	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdh.c.obj	f636fe22323df1a8
3983	4039	1752514694682120698	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves_new.c.obj	b2cf98945e0888a5
3995	4044	1752514694694257522	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy_poll.c.obj	11f537d1b45bcedb
3872	4053	1752514694571446197	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher.c.obj	8971071723c72c61
3952	4056	1752514694651386429	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdsa.c.obj	69c4ad2e306fe97a
4039	4082	1752514694738139906	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hkdf.c.obj	a57a3cfffcc6003c
3986	4085	1752514694685297571	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy.c.obj	96a1baff5b745bab
3533	4092	1752514694232388992	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crt.c.obj	1b76b366e974f3d8
3353	4123	1752514694052120376	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls.c.obj	40b896d207704bb2
4092	4138	1752514694790978742	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/memory_buffer_alloc.c.obj	88686c76d6d0ada8
4044	4140	1752514694743604944	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hmac_drbg.c.obj	3baf63771253c72e
4013	4144	1752514694711837801	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/error.c.obj	7297a5cb06b62fd7
4086	4150	1752514694785140080	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md5.c.obj	7dfdc97837bd98b8
4053	4157	1752514694752284146	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lmots.c.obj	d6b517c67ae58d98
4027	4160	1752514694726320748	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/gcm.c.obj	c85ded2f5850602b
3767	4173	1752514694466261067	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum.c.obj	99ad94cfa8e0d0de
4123	4185	1752514694822114302	esp-idf/mbedtls/mbedtls/library/libmbedtls.a	1fc61eec63c274c3
4138	4186	1752514694837126749	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/nist_kw.c.obj	ca944655edca75c9
4056	4187	1752514694755312727	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lms.c.obj	c660cf3da2897164
4144	4197	1752514694843059537	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/padlock.c.obj	416753cc9a47c27b
4160	4235	1752514694859386774	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_ecc.c.obj	3ff783c0d48d70c5
4082	4243	1752514694781604749	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md.c.obj	f0caa25105da9c27
4150	4261	1752514694849163199	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pem.c.obj	c4aa0df839b48ef7
3967	4280	1752514694666128043	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves.c.obj	d32a56738c05e65c
4261	4311	1752514694960285366	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/poly1305.c.obj	68376193e454bffd
4174	4314	1752514694873374806	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_wrap.c.obj	dd9eb2a0ef35b41a
4235	4320	1752514694934100677	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform.c.obj	d81056c0ebfe48
4245	4327	1752514694944211669	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform_util.c.obj	d54c0864a63f5215
4186	4328	1752514694885703546	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs5.c.obj	5091d99ccb16cc08
4185	4328	1752514694884192214	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs12.c.obj	ef0ccc5bb3b142f4
4140	4332	1752514694839545914	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/oid.c.obj	f650adc480b01426
4197	4362	1752514694896029622	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkwrite.c.obj	e0dfb1fef718546e
4158	4397	1752514694856831901	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk.c.obj	be8d986e6cde2d11
4320	4399	1752514695019451238	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_client.c.obj	14c3e5c9f178617
4328	4404	1752514695027445816	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ffdh.c.obj	7b794b998b3ae727
4327	4406	1752514695026290233	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_driver_wrappers_no_static.c.obj	16e679f781a3d89f
4187	4413	1752514694886494712	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkparse.c.obj	bdf4fa437e58813
4312	4413	1752514695010779911	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_aead.c.obj	32408f3c14e86789
4333	4423	1752514695032021687	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_hash.c.obj	ce6c8f274d032073
4404	4454	1752514695103342134	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_se.c.obj	3f3e28a05bef435d
3959	4456	1752514694658198466	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp.c.obj	4be35c9ed1fa187d
4414	4458	1752514695112801919	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_its_file.c.obj	7b19a6cf285ddac4
4413	4460	1752514695112138919	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_storage.c.obj	d56e3b61ef3dfed9
4397	4477	1752514695097082097	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_pake.c.obj	e6d11f8f5c89140
4362	4482	1752514695061481665	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_mac.c.obj	1bc478f93546089f
4314	4490	1752514695012965910	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_cipher.c.obj	72bd184511f4595
4455	4496	1752514695153788763	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ripemd160.c.obj	8d44b117a2686ad7
4328	4520	1752514695026954566	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ecp.c.obj	3185ef60a2e977f9
4423	4528	1752514695122439370	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_util.c.obj	2d60bf385b5e4c0c
4461	4531	1752514695159780509	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha1.c.obj	45567606ef1a17a9
4491	4537	1752514695189838403	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha3.c.obj	97e5ae1ea32c2e1e
4458	4541	1752514695157093302	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa_alt_helpers.c.obj	1be32db2731feeb
4496	4542	1752514695195204816	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/threading.c.obj	7af7ac4755a36476
4399	4546	1752514695098382263	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_rsa.c.obj	339b63508fe17c29
4482	4557	1752514695181110159	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha512.c.obj	fa4171e101001d76
4520	4564	1752514695219370589	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/timing.c.obj	35b6c9b808b8fcfe
4406	4567	1752514695105066341	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_slot_management.c.obj	bb5432061e0ffc6a
4477	4570	1752514695176716496	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha256.c.obj	2399beb38c17e37b
4528	4589	1752514695227590250	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version.c.obj	8bc138af0ac6a1a8
4531	4591	1752514695230519498	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version_features.c.obj	f18f83a29ea73090
4537	4607	1752514695236560785	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/esp_sha_gdma_impl.c.obj	de1fac234cf18799
4541	4616	1752514695240250865	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/dma/esp_aes_gdma_impl.c.obj	ce72582c537bd721
4564	4619	1752514695263286723	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_mem.c.obj	d7f8b9421a8209b
4557	4623	1752514695256234270	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_hardware.c.obj	6c576226c9236092
4568	4627	1752514695267132679	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_timing.c.obj	4a24c888af98521f
4570	4645	1752514695269737843	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/esp_aes_xts.c.obj	1b50d12da3aabca4
4590	4661	1752514695289678120	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/esp_aes_common.c.obj	4c98155520b7d6f3
4547	4663	1752514695245909986	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/crypto_shared_gdma/esp_crypto_shared_gdma.c.obj	e3ab6f9cd3ad278f
4607	4680	1752514695306192358	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/esp_sha.c.obj	d6d4664930ea3f94
4623	4711	1752514695321956096	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/bignum/bignum_alt.c.obj	f9e16e38e044628e
4627	4714	1752514695326218760	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/esp_sha1.c.obj	6b601844e9046870
4619	4716	1752514695318738932	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/bignum/esp_bignum.c.obj	33a258cb53f418f
4645	4721	1752514695344641829	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/esp_sha256.c.obj	ceb7e0b2208c8981
4591	4724	1752514695290107370	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/dma/esp_aes.c.obj	2aba6c9786b89809
4661	4743	1752514695360621192	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/esp_sha512.c.obj	519fda9b7d1b2ad1
4680	4744	1752514695379234345	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/ecc/esp_ecc.c.obj	f8d7890271f3ec17
4617	4745	1752514695315816976	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/sha.c.obj	34b926696fd9d3c7
4716	4768	1752514695415459860	esp-idf/mbedtls/mbedtls/library/libmbedx509.a	ad43c1344f507acd
4724	4786	1752514695423319354	esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m/p256-m.c.obj	292b360c537d762d
4768	4786	1752514695483564726	bootloader-prefix/src/bootloader-stamp/bootloader-mkdir	4cf974dba7a3bf1f
4768	4786	1752514695483564726	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir	4cf974dba7a3bf1f
4711	4790	1752514695409971239	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/ecc/ecc_alt.c.obj	de5b97e5d5675b04
4722	4790	1752514695420908564	esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m_driver_entrypoints.c.obj	f74c33a50af3c8dd
4745	4791	1752514695443951755	esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/Hacl_Curve25519_joined.c.obj	1f83ace8daee6b64
4714	4791	1752514695413569111	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/md/esp_md.c.obj	1a9eff181f56dcc5
4744	4791	1752514695442935714	esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/x25519.c.obj	9f722c7fd80148ad
4743	4800	1752514695442503131	esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/everest.c.obj	5dd74a0b4f9482d6
4787	4801	1752514695500074672	bootloader-prefix/src/bootloader-stamp/bootloader-download	8ea2bb5bdcfdff7d
4787	4801	1752514695500074672	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-download	8ea2bb5bdcfdff7d
4542	4808	1752514695240831948	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/dma/esp_aes_dma_core.c.obj	d92aacf2381a077e
4801	4813	1752514695511769746	bootloader-prefix/src/bootloader-stamp/bootloader-update	bcaae8c355589155
4801	4813	1752514695511769746	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-update	bcaae8c355589155
4663	4817	1752514695362603316	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/esp_aes_gcm.c.obj	7118d09e6866248e
4813	4825	1752514695523522821	bootloader-prefix/src/bootloader-stamp/bootloader-patch	cdc19c58f60d12f7
4813	4825	1752514695523522821	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch	cdc19c58f60d12f7
4456	4841	1752514695155420679	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa.c.obj	95c0aeff708550b7
4280	5072	1752514694979455185	esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto.c.obj	b9b1f9181c77fe1f
5072	5212	1752514695770848262	esp-idf/mbedtls/mbedtls/library/libmbedcrypto.a	97be4840cf6d2791
5212	5231	1752514695911228574	esp-idf/mbedtls/mbedtls/3rdparty/p256-m/libp256m.a	4340323362e67317
5231	5252	1752514695930675851	esp-idf/mbedtls/mbedtls/3rdparty/everest/libeverest.a	25594854c0bd0e6e
5252	5554	1752514696245323241	esp-idf/mbedtls/x509_crt_bundle	95076e1273cdc625
5252	5554	1752514696245323241	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/mbedtls/x509_crt_bundle	95076e1273cdc625
5554	5644	1752514696342162752	x509_crt_bundle.S	6e47b546b796b461
5554	5644	1752514696342162752	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/x509_crt_bundle.S	6e47b546b796b461
5644	5708	1752514696343305252	esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/__/__/x509_crt_bundle.S.obj	25dc246d79862865
5645	5718	1752514696344198959	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_init.c.obj	bf6b4de14fb375a8
5644	5720	1752514696343563543	esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_trace.c.obj	b9ee4ecdb5b2d677
5644	5722	1752514696343433293	esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_locks.c.obj	87a90178136546fd
5645	5728	1752514696344600167	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/system_time.c.obj	a9a3ea717c068f6d
5646	5732	1752514696344765209	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_common.c.obj	dd64c16cda1c6e9b
5646	5737	1752514696344953167	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_systimer.c.obj	4831ad2a9ed1e49d
5645	5742	1752514696344388751	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/ets_timer_legacy.c.obj	a7b14259dd62f57a
5644	5769	1752514696343096210	esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/esp_crt_bundle/esp_crt_bundle.c.obj	8c6d7a5b1333b29a
5708	5770	1752514696407447787	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_etm.c.obj	56f625ba8fdd2a98
5645	5787	1752514696344008584	esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer.c.obj	d9573465add4af
5720	5796	1752514696419174945	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_glitch_filter_ops.c.obj	e61f3700a0246b11
5644	5809	1752514696343715376	esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_impl.c.obj	f243910baaf167a9
5769	5814	1752514696468769908	esp-idf/mbedtls/libmbedtls.a	c79e26751fa41045
5770	5817	1752514696469156699	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/instruction_decode.c.obj	d9942d42779e9f10
5796	5824	1752514696495030222	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/vectors.S.obj	b941138680aa6bf
5817	5842	1752514696516669997	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/vectors_clic.S.obj	937077140bb6d166
5787	5846	1752514696486239937	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/interrupt.c.obj	5fd2c609af585d43
5737	5848	1752514696436227141	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_flex_glitch_filter.c.obj	14f1c3bf4619beba
5814	5855	1752514696513558708	esp-idf/esp_pm/libesp_pm.a	cd4a4ed579cd706
5732	5867	1752514696431130228	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_pin_glitch_filter.c.obj	3de726d152517e44
5728	5874	1752514696426960148	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/dedic_gpio.c.obj	b3f81ee9466c9d
5810	5876	1752514696508887003	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/interrupt_clic.c.obj	76aaf1dc89fa92f0
5855	5901	1752514696554571011	esp-idf/esp_timer/libesp_timer.a	e4ffd29b97dc2830
5722	5932	1752514696421722027	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/rtc_io.c.obj	c2d340e1d0114060
5874	5957	1752514696573002747	esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_private.c.obj	bf76dc697c83b4ba
5742	5962	1752514696441156345	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_etm.c.obj	50bc6d883250e51c
5848	5981	1752514696547380391	esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_helpers.c.obj	b4421af0204ccde2
5846	6039	1752514696545353893	esp-idf/usb/CMakeFiles/__idf_usb.dir/hub.c.obj	8ae97b353e5da834
5981	6054	1752514696680098876	esp-idf/espressif__usb_host_cdc_acm/CMakeFiles/__idf_espressif__usb_host_cdc_acm.dir/cdc_host_descriptor_parsing.c.obj	bea2a0bf77d01d46
5901	6083	1752514696600386768	esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_phy.c.obj	f8803c34af6398cf
5842	6084	1752514696541507062	esp-idf/usb/CMakeFiles/__idf_usb.dir/enum.c.obj	192b517ef224d262
6039	6121	1752514696738464332	esp-idf/espressif__usb_host_cdc_acm/CMakeFiles/__idf_espressif__usb_host_cdc_acm.dir/cdc_host_acm_compliant.c.obj	b6bf93178c1cda79
5957	6129	1752514696656289310	esp-idf/usb/CMakeFiles/__idf_usb.dir/ext_port.c.obj	ca891a7d3216ca05
6055	6129	1752514696753921279	esp-idf/espressif__usb_host_cdc_acm/CMakeFiles/__idf_espressif__usb_host_cdc_acm.dir/cdc_host_ops.c.obj	b923e364cdd3d560
6084	6164	1752514696783267507	esp-idf/espressif__usb_host_cp210x_vcp/CMakeFiles/__idf_espressif__usb_host_cp210x_vcp.dir/usb_host_cp210x_vcp.c.obj	8eacb816fc2b3e57
5876	6166	1752514696575133829	esp-idf/usb/CMakeFiles/__idf_usb.dir/usbh.c.obj	5e366f0b419bb411
5962	6168	1752514696661531139	esp-idf/espressif__usb_host_cdc_acm/CMakeFiles/__idf_espressif__usb_host_cdc_acm.dir/cdc_acm_host.c.obj	a59ab73b46e449dc
6084	6169	1752514696782813841	esp-idf/espressif__usb_host_ch34x_vcp/CMakeFiles/__idf_espressif__usb_host_ch34x_vcp.dir/usb_host_ch34x_vcp.c.obj	fd47c790e6792c17
5932	6180	1752514696631494537	esp-idf/usb/CMakeFiles/__idf_usb.dir/ext_hub.c.obj	d892de1d82593144
5867	6191	1752514696566386960	esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_host.c.obj	60109b46d6cee9d8
5824	6223	1752514696522822160	esp-idf/usb/CMakeFiles/__idf_usb.dir/hcd_dwc.c.obj	3754c2cc79c89607
5718	6224	1752514696417173821	esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio.c.obj	a9207dbc77ac9f2e
6224	6254	1752514696923633902	esp-idf/esp_driver_gpio/libesp_driver_gpio.a	410eacc88d949187
6254	6279	1752514696953425297	esp-idf/riscv/libriscv.a	7f560fb911cabe4c
6279	6318	1752514696978705486	esp-idf/usb/libusb.a	e5133ec339de1b16
6318	6345	1752514697017375583	esp-idf/espressif__usb_host_cdc_acm/libespressif__usb_host_cdc_acm.a	168d0b818db7b740
6345	6367	1752514697043943729	esp-idf/espressif__usb_host_ch34x_vcp/libespressif__usb_host_ch34x_vcp.a	5da31ef679938060
6345	6367	1752514697044203271	esp-idf/espressif__usb_host_cp210x_vcp/libespressif__usb_host_cp210x_vcp.a	5cbceb64d9a4e1be
6121	6844	1752514696820159855	esp-idf/espressif__usb_host_ftdi_vcp/CMakeFiles/__idf_espressif__usb_host_ftdi_vcp.dir/usb_host_ftdi_vcp.cpp.obj	b8775d6f17955ad8
6129	6863	1752514696827945224	esp-idf/espressif__usb_host_vcp/CMakeFiles/__idf_espressif__usb_host_vcp.dir/usb_host_vcp.cpp.obj	4a07b595dfbc1cdb
6845	6866	1752514697543846815	esp-idf/espressif__usb_host_ftdi_vcp/libespressif__usb_host_ftdi_vcp.a	447d67e51bb14067
6863	6884	1752514697562089885	esp-idf/espressif__usb_host_vcp/libespressif__usb_host_vcp.a	72051d20b0e37164
6129	6911	1752514696828401932	esp-idf/main/CMakeFiles/__idf_main.dir/cdc_acm_vcp_example_main.cpp.obj	700f5c1165d28de4
6911	6932	1752514697610406099	esp-idf/main/libmain.a	ceb91c800348ac3
4825	7194	1752514697892402597	bootloader-prefix/src/bootloader-stamp/bootloader-configure	822072e60b8f3a6e
4825	7194	1752514697892402597	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure	822072e60b8f3a6e
7194	8539	1752514697893079638	bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
7194	8539	1752514697893079638	bootloader/bootloader.elf	9b0e7e8290f7e2bb
7194	8539	1752514697893079638	bootloader/bootloader.bin	9b0e7e8290f7e2bb
7194	8539	1752514697893079638	bootloader/bootloader.map	9b0e7e8290f7e2bb
7194	8539	1752514697893079638	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
7194	8539	1752514697893079638	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.elf	9b0e7e8290f7e2bb
7194	8539	1752514697893079638	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.bin	9b0e7e8290f7e2bb
7194	8539	1752514697893079638	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.map	9b0e7e8290f7e2bb
8539	8547	1752514699238686885	bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
8539	8547	1752514699238686885	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
8547	8564	1752514699262828867	CMakeFiles/bootloader-complete	cc468f9d684748b4
8547	8564	1752514699262828867	bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
8547	8564	1752514699262828867	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader-complete	cc468f9d684748b4
8547	8564	1752514699262828867	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
6932	8622	1752514699306115293	esp-idf/esp_system/ld/sections.ld	bcfa7a2684b8bb41
6932	8622	1752514699306115293	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_system/ld/sections.ld	bcfa7a2684b8bb41
8622	8644	1752514699320836949	CMakeFiles/cdc_acm_vcp.elf.dir/project_elf_src_esp32p4.c.obj	75fce373588b48a2
8644	8944	1752514699343449265	cdc_acm_vcp.elf	aea6503ee5c34629
8944	9052	1752514699750518004	.bin_timestamp	3ea584a2cdd203c8
8944	9052	1752514699750518004	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/.bin_timestamp	3ea584a2cdd203c8
9052	9085	1752514699750974045	esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
9052	9085	1752514699750974045	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
34	68	1752514764007441358	esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
34	68	1752514764007441358	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
34	82	1752514764007034942	bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
34	82	1752514764007034942	bootloader/bootloader.elf	9b0e7e8290f7e2bb
34	82	1752514764007034942	bootloader/bootloader.bin	9b0e7e8290f7e2bb
34	82	1752514764007034942	bootloader/bootloader.map	9b0e7e8290f7e2bb
34	82	1752514764007034942	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
34	82	1752514764007034942	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.elf	9b0e7e8290f7e2bb
34	82	1752514764007034942	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.bin	9b0e7e8290f7e2bb
34	82	1752514764007034942	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.map	9b0e7e8290f7e2bb
82	90	1752514764055482867	bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
82	90	1752514764055482867	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
90	106	1752514764078588000	CMakeFiles/bootloader-complete	cc468f9d684748b4
90	106	1752514764078588000	bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
90	106	1752514764078588000	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader-complete	cc468f9d684748b4
90	106	1752514764078588000	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
20	55	1752514788067497836	esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
20	55	1752514788067497836	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
20	70	1752514788067027920	bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
20	70	1752514788067027920	bootloader/bootloader.elf	9b0e7e8290f7e2bb
20	70	1752514788067027920	bootloader/bootloader.bin	9b0e7e8290f7e2bb
20	70	1752514788067027920	bootloader/bootloader.map	9b0e7e8290f7e2bb
20	70	1752514788067027920	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
20	70	1752514788067027920	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.elf	9b0e7e8290f7e2bb
20	70	1752514788067027920	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.bin	9b0e7e8290f7e2bb
20	70	1752514788067027920	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.map	9b0e7e8290f7e2bb
70	78	1752514788117035758	bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
70	78	1752514788117035758	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
78	95	1752514788142031000	CMakeFiles/bootloader-complete	cc468f9d684748b4
78	95	1752514788142031000	bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
78	95	1752514788142031000	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader-complete	cc468f9d684748b4
78	95	1752514788142031000	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
20	54	1752514801205695726	esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
20	54	1752514801205695726	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
20	68	1752514801205121602	bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
20	68	1752514801205121602	bootloader/bootloader.elf	9b0e7e8290f7e2bb
20	68	1752514801205121602	bootloader/bootloader.bin	9b0e7e8290f7e2bb
20	68	1752514801205121602	bootloader/bootloader.map	9b0e7e8290f7e2bb
20	68	1752514801205121602	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
20	68	1752514801205121602	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.elf	9b0e7e8290f7e2bb
20	68	1752514801205121602	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.bin	9b0e7e8290f7e2bb
20	68	1752514801205121602	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.map	9b0e7e8290f7e2bb
68	75	1752514801253113733	bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
68	75	1752514801253113733	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
75	92	1752514801276413000	CMakeFiles/bootloader-complete	cc468f9d684748b4
75	92	1752514801276413000	bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
75	92	1752514801276413000	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader-complete	cc468f9d684748b4
75	92	1752514801276413000	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
26	59	1752514941952880208	esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
26	59	1752514941952880208	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esptool_py/CMakeFiles/app_check_size	7b0a2b7d282da11c
25	74	1752514941952456084	bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
25	74	1752514941952456084	bootloader/bootloader.elf	9b0e7e8290f7e2bb
25	74	1752514941952456084	bootloader/bootloader.bin	9b0e7e8290f7e2bb
25	74	1752514941952456084	bootloader/bootloader.map	9b0e7e8290f7e2bb
25	74	1752514941952456084	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-build	9b0e7e8290f7e2bb
25	74	1752514941952456084	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.elf	9b0e7e8290f7e2bb
25	74	1752514941952456084	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.bin	9b0e7e8290f7e2bb
25	74	1752514941952456084	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.map	9b0e7e8290f7e2bb
74	81	1752514942001304339	bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
74	81	1752514942001304339	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-install	974c3394d50c2dd6
81	98	1752514942024178000	CMakeFiles/bootloader-complete	cc468f9d684748b4
81	98	1752514942024178000	bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
81	98	1752514942024178000	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader-complete	cc468f9d684748b4
81	98	1752514942024178000	/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-done	cc468f9d684748b4
