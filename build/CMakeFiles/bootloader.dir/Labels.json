{"sources": [{"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader.rule"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader-complete.rule"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}