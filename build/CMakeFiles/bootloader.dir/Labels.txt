# Target labels
 bootloader
# Source files and their labels
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader.rule
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/CMakeFiles/bootloader-complete.rule
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
