# Load esp32p4 ROM ELF symbols
define target hookpost-remote
set confirm off
  # if $_streq((char *) 0x4fc1d1ac, "Aug 11 2023")
  if (*(int*) 0x4fc1d1ac) == 0x20677541 && (*(int*) 0x4fc1d1b0) == 0x32203131 && (*(int*) 0x4fc1d1b4) == 0x333230
    add-symbol-file /Users/<USER>/.espressif/tools/esp-rom-elfs/20241011/esp32p4_rev0_rom.elf
  else
    echo Warning: Unknown esp32p4 ROM revision.\n
  end
set confirm on
end


# Load bootloader symbols
set confirm off
    add-symbol-file /Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/bootloader/bootloader.elf
set confirm on

# Load application symbols
file /Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/cdc_acm_vcp.elf
