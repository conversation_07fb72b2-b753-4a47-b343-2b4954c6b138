# Install script for directory: /Users/<USER>/esp/v5.4.1/esp-idf

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/usr/local")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "TRUE")
endif()

# Set path to fallback-tool for dependency-resolution.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/riscv32-esp-elf-objdump")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/riscv/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_driver_gpio/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_timer/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_pm/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/mbedtls/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/bootloader/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esptool_py/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/partition_table/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_app_format/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_bootloader_format/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/app_update/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_partition/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/efuse/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/bootloader_support/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_mm/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/spi_flash/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_system/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_common/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_rom/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/hal/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/log/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/heap/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/soc/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_security/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/esp_hw_support/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/freertos/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/newlib/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/pthread/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/cxx/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/usb/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/espressif__usb_host_cdc_acm/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/espressif__usb_host_ch34x_vcp/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/espressif__usb_host_cp210x_vcp/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/espressif__usb_host_ftdi_vcp/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/espressif__usb_host_vcp/cmake_install.cmake")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for the subdirectory.
  include("/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/main/cmake_install.cmake")
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
if(CMAKE_INSTALL_LOCAL_ONLY)
  file(WRITE "/Users/<USER>/Programming/ESP-IDF projects/Test Full Speed USB Esp32p4/cdc_acm_vcp/build/esp-idf/install_local_manifest.txt"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
